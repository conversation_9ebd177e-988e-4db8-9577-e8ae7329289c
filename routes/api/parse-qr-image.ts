import { Handlers } from "$fresh/server.ts";
import { parseAuthenticatorURL } from "../../lib/totp.ts";

export const handler: Handlers = {
  async POST(req) {
    try {
      const formData = await req.formData();
      const file = formData.get("image") as File;

      if (!file) {
        return new Response(JSON.stringify({
          success: false,
          error: "没有提供图片文件"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      console.log("收到二维码解析请求，文件类型:", file.type, "文件大小:", file.size);

      // 方案1: 尝试简单的文本解析（如果用户直接上传了包含 URL 的文本文件）
      try {
        if (file.type.startsWith("text/")) {
          const text = await file.text();
          const parsed = parseAuthenticatorURL(text.trim());
          if (parsed) {
            console.log("文本解析成功");
            return new Response(JSON.stringify({
              success: true,
              data: parsed,
              method: "text-parsing"
            }), {
              headers: { "Content-Type": "application/json" }
            });
          }
        }
      } catch (error) {
        console.log("文本解析失败:", error);
      }

      // 方案2: 尝试第三方 API 解析
      try {
        console.log("尝试第三方 API 解析...");
        const thirdPartyResult = await parseWithThirdPartyAPI(file);
        if (thirdPartyResult) {
          console.log("第三方 API 返回:", thirdPartyResult);
          const parsed = parseAuthenticatorURL(thirdPartyResult);
          if (parsed) {
            console.log("第三方 API 解析成功");
            return new Response(JSON.stringify({
              success: true,
              data: parsed,
              method: "third-party-api"
            }), {
              headers: { "Content-Type": "application/json" }
            });
          }
        }
      } catch (error) {
        console.log("第三方 API 解析失败:", error);
      }

      // 方案3: 尝试其他第三方 API
      try {
        console.log("尝试备用 API 解析...");
        const alternativeResult = await parseWithAlternativeAPI(file);
        if (alternativeResult) {
          console.log("备用 API 返回:", alternativeResult);
          const parsed = parseAuthenticatorURL(alternativeResult);
          if (parsed) {
            console.log("备用 API 解析成功");
            return new Response(JSON.stringify({
              success: true,
              data: parsed,
              method: "alternative-api"
            }), {
              headers: { "Content-Type": "application/json" }
            });
          }
        }
      } catch (error) {
        console.log("备用 API 解析失败:", error);
      }

      // 如果所有方法都失败了
      console.log("所有解析方法都失败了");
      return new Response(JSON.stringify({
        success: false,
        error: "无法解析二维码。请尝试：\n1. 确保图片清晰且包含完整的二维码\n2. 尝试使用客户端解析（刷新页面重试）\n3. 手动输入认证器信息"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      console.error("服务器处理错误:", error);
      return new Response(JSON.stringify({
        success: false,
        error: "服务器处理错误: " + (error as Error).message
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }
  }
};

/**
 * 使用第三方 API 解析二维码 - qr-server.com
 */
async function parseWithThirdPartyAPI(file: File): Promise<string | null> {
  try {
    // 将文件转换为 base64
    const arrayBuffer = await file.arrayBuffer();
    const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
    
    // 使用 qrserver.com 的解析 API
    const response = await fetch("https://api.qrserver.com/v1/read-qr-code/", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: `fileurl=data:${file.type};base64,${base64}`
    });

    if (response.ok) {
      const result = await response.json();
      if (result && result[0] && result[0].symbol && result[0].symbol[0]) {
        return result[0].symbol[0].data;
      }
    }
    return null;
  } catch (error) {
    console.error("qrserver.com API 错误:", error);
    return null;
  }
}

/**
 * 使用备用第三方 API 解析二维码 - goqr.me
 */
async function parseWithAlternativeAPI(file: File): Promise<string | null> {
  try {
    const formData = new FormData();
    formData.append("file", file);

    const response = await fetch("https://api.goqr.me/api/read-qr-code/", {
      method: "POST",
      body: formData
    });

    if (response.ok) {
      const result = await response.json();
      if (result && result[0] && result[0].symbol && result[0].symbol[0]) {
        return result[0].symbol[0].data;
      }
    }
    return null;
  } catch (error) {
    console.error("goqr.me API 错误:", error);
    return null;
  }
}
