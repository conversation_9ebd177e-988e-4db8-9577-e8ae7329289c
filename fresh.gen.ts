// DO NOT EDIT. This file is generated by Fresh.
// This file SHOULD be checked into source version control.
// This file is automatically updated during development when running `dev.ts`.

import * as $_404 from "./routes/_404.tsx";
import * as $_app from "./routes/_app.tsx";
import * as $add from "./routes/add.tsx";
import * as $api_authenticators_id_ from "./routes/api/authenticators/[id].ts";
import * as $api_joke from "./routes/api/joke.ts";
import * as $api_parse_qr from "./routes/api/parse-qr.ts";
import * as $dashboard from "./routes/dashboard.tsx";
import * as $greet_name_ from "./routes/greet/[name].tsx";
import * as $index from "./routes/index.tsx";
import * as $login from "./routes/login.tsx";
import * as $logout from "./routes/logout.tsx";
import * as $register from "./routes/register.tsx";
import * as $AuthenticatorList from "./islands/AuthenticatorList.tsx";
import * as $Counter from "./islands/Counter.tsx";
import * as $QRCodeUploader from "./islands/QRCodeUploader.tsx";
import type { Manifest } from "$fresh/server.ts";

const manifest = {
  routes: {
    "./routes/_404.tsx": $_404,
    "./routes/_app.tsx": $_app,
    "./routes/add.tsx": $add,
    "./routes/api/authenticators/[id].ts": $api_authenticators_id_,
    "./routes/api/joke.ts": $api_joke,
    "./routes/api/parse-qr.ts": $api_parse_qr,
    "./routes/dashboard.tsx": $dashboard,
    "./routes/greet/[name].tsx": $greet_name_,
    "./routes/index.tsx": $index,
    "./routes/login.tsx": $login,
    "./routes/logout.tsx": $logout,
    "./routes/register.tsx": $register,
  },
  islands: {
    "./islands/AuthenticatorList.tsx": $AuthenticatorList,
    "./islands/Counter.tsx": $Counter,
    "./islands/QRCodeUploader.tsx": $QRCodeUploader,
  },
  baseUrl: import.meta.url,
} satisfies Manifest;

export default manifest;
