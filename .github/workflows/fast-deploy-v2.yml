name: Fast Deploy (Deno 2.0 Optimized)

on:
  push:
    branches: [main]
    paths-ignore:
      - '**.md'
      - '.gitignore'
      - 'LICENSE'
      - 'DENO2_OPTIMIZATION.md'
      - 'BUILD_OPTIMIZATION.md'
      - 'PERFORMANCE.md'

jobs:
  fast-deploy:
    name: Ultra Fast Deploy
    runs-on: ubuntu-latest
    
    permissions:
      id-token: write
      contents: read

    steps:
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Setup Deno 2.x
        uses: denoland/setup-deno@v2
        with:
          deno-version: v2.x

      # 最小化缓存
      - name: <PERSON><PERSON> <PERSON><PERSON>
        uses: actions/cache@v4
        with:
          path: ~/.cache/deno
          key: ${{ runner.os }}-deno2-${{ hashFiles('deno.json') }}
          restore-keys: ${{ runner.os }}-deno2-

      # 超快构建（已验证有效）
      - name: Deno 2.0 Ultra Fast Build
        timeout-minutes: 2
        env:
          DENO_FUTURE: 1
          DENO_NO_PACKAGE_JSON: 1
          DENO_NO_UPDATE_CHECK: 1
        run: |
          echo "🚀 Starting ultra fast build..."
          echo "📦 Deno $(deno --version | head -1)"
          
          # 使用已验证的优化构建
          deno task build:deno2
          
          echo "✅ Build completed in seconds!"

      # 直接部署
      - name: Deploy to Deno Deploy
        uses: denoland/deployctl@v1
        with:
          project: "denoauthent"
          entrypoint: "main.ts"
          root: "."
