name: Deploy

on:
  push:
    branches: [main]
    paths-ignore:
      - '**.md'
      - '.gitignore'
      - 'LICENSE'

jobs:
  deploy:
    name: Deploy to Deno Deploy
    runs-on: ubuntu-latest
    
    permissions:
      id-token: write
      contents: read

    steps:
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Setup Deno
        uses: denoland/setup-deno@v2
        with:
          deno-version: v2.x

      # 轻量级缓存（Deno 2.0 优化）
      - name: Cache Deno dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/deno
            ~/.deno
          key: ${{ runner.os }}-deno-v2-${{ hashFiles('deno.json') }}
          restore-keys: |
            ${{ runner.os }}-deno-v2-
            ${{ runner.os }}-deno-

      # 快速依赖缓存
      - name: Cache dependencies
        timeout-minutes: 1
        run: |
          echo "Caching dependencies..."
          deno cache --quiet --no-check main.ts || echo "Cache completed"

      # Deno 2.0 超快构建
      - name: Ultra Fast Build (Deno 2.0 Optimized)
        timeout-minutes: 2
        env:
          DENO_FUTURE: 1
          DENO_NO_PACKAGE_JSON: 1
          DENO_NO_UPDATE_CHECK: 1
        run: |
          echo "🚀 Starting Deno 2.0 ultra fast build..."
          echo "📦 Deno version: $(deno --version | head -1)"

          # 直接使用已验证的优化构建
          deno task build:deno2 || {
            echo "⚠️ Optimized build failed, using minimal build..."
            deno task build:minimal
          }

          echo "✅ Build completed successfully"

      # 部署到 Deno Deploy
      - name: Deploy to Deno Deploy
        uses: denoland/deployctl@v1
        with:
          project: "denoauthent"
          entrypoint: "main.ts"
          root: "."
